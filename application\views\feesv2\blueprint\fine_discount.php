<!-- START BREADCRUMB -->
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_blueprint');?>">Fee Blueprint</a></li>
  <li class="active">Fine and discount</li>
</ul>

<style>
.fine-rule {
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}
.fine-rule:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.fine-builder-header {
  background-color: #337ab7;
  color: white;
  padding: 10px 15px;
  margin: -15px -15px 15px -15px;
  border-radius: 3px 3px 0 0;
}
.period-help {
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}
.json-preview {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  padding: 10px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
.fine-rule .form-control:focus {
  border-color: #337ab7;
  box-shadow: 0 0 5px rgba(51, 122, 183, 0.3);
}
.btn-group-fine {
  margin-top: 10px;
}
.btn-group-fine .btn {
  margin-right: 5px;
}
.rule-counter {
  background-color: #337ab7;
  color: white;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
</style>

<div class="page-content-wrap">
  <form enctype="multipart/form-data" method="post" id="demo-form" action="<?php echo site_url('feesv2/fees_blueprint/update_fine_discount_alogo');?>" data-parsley-validate="" class="form-horizontal">
  <input type="hidden" name="feev2_blueprint_id" value="<?= $feev2_blueprint_id ?>">
  <div class="row">
    <div class="col-md-8">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">Add Fine and Discount</h3>
        </div>

        <div class="panel-body">
          
           <div class="form-group" id="staff_discountAmount" style="display: none">
              <label class="col-md-2 control-label" for="discount_amount_algo">Discount</label>
            <div class="col-md-7"> 
              <input type="number" placeholder="Amount or Percentage" id="discount_amountId" class="form-control" name="discount_amount">
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="discount_amount_algo"> Discount Algorithm</label>  
            <div class="col-md-7">
              <select class="form-control" name="discount_amount_algo" id="discount_amount_algo" >
                <option value="none">None</option>
                <option value="manual_p">Manually Enter Percentage</option>
                <option value="manual_num">Manually Enter Amount</option>
                <option value="discount_if_full_paid_num">Discount if full paid number</option>
                <option value="discount_if_full_paid_p">Discount if full paid percentage</option>
                <option value="discount_if_full_paid_json">Discount in JSON Format</option>
              </select>
            </div>
          </div>


          <div class="form-group" id="discountAmount" style="display: none">
              <label class="col-md-2 control-label" for="discount_amountId">Discount</label>
            <div class="col-md-7"> 
              <input type="number" placeholder="Amount or Percentage" id="discount_amountId" class="form-control" name="discount_amount">
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="allocation_algo">Allocation Algorithm</label>
            <div class="col-md-7">
              <select class="form-control" name="allocation_algo">
                <option value="none">None</option>
                <option value="equal">Equal across all installments</option>
                <option value="custom">Custom</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="algo_params">Algorithm Parameters</label>  
            <div class="col-md-7">
              <input name="algo_params" id="allocation_params" class="form-control" placeholder="Algorithm Params" type="text"/>
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="fine_amount_algo"> Fine Amount Alog</label>  
            <div class="col-md-7">
              <select class="form-control" name="fine_amount_algo" id="fine_amount_algo">
                <option value="none">None</option>
                <option value="manually_enter">Manually entry</option>
                <option value="fine_per_day">Fine per Day</option>
                <option value="fine_per_week">Fine per Week</option>
                <option value="fine_per_month">Fine per Month</option>
                <option value="fixed_fine">Fixed Fine</option>
                <option value="fine_json">Use JSON (Days/Weeks/Months)</option>
                <option value="fine_json_bimonthly">Use JSON (Bimonthly)</option>
                <option value="fine_date_range">Date Range Fine</option>
              </select>
            </div>
          </div>

          <div class="form-group" id="previousfineAmount" style="display: none">
            <label class="col-md-2 control-label">Assinged Amount  </label>  
            <div class="col-md-7">
              <input id="previous_amount_assinged" readonly class="form-control" type="text"/>
            </div>
          </div>


           <div class="form-group" id="fineAmount" style="display: none">
            <label class="col-md-2 control-label" for="algo_params">Fine Amount  </label>  
            <div class="col-md-7">
              <input name="fine_amount_params" id="fine_amount_params" class="form-control" placeholder="Fine amount Params" type="text"/>
            </div>
          </div>

          <!-- Dynamic Fine Configuration UI -->
          <div class="form-group" id="fineJsonBuilder" style="display: none">
            <label class="col-md-2 control-label">Fine Configuration</label>
            <div class="col-md-10">
              <div class="panel panel-default">
                <div class="panel-body">
                  <div class="fine-builder-header">
                    <h4 style="margin: 0;">
                      <i class="fa fa-cogs"></i>
                      <span id="builderTitle">Fine Rule Builder</span>
                    </h4>
                    <small id="builderDescription">Configure your fine rules using the form below</small>
                  </div>
                  <div id="fineConfigContainer">
                    <!-- Dynamic fine configuration will be added here -->
                  </div>
                  <div style="margin-top: 15px;">
                    <button type="button" class="btn btn-sm btn-success" id="addFineRule">
                      <i class="fa fa-plus"></i> Add Rule
                    </button>
                    <button type="button" class="btn btn-sm btn-info" id="previewJson">
                      <i class="fa fa-eye"></i> Preview JSON
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" id="loadFromJson">
                      <i class="fa fa-upload"></i> Load from JSON
                    </button>
                    <button type="button" class="btn btn-sm btn-default" id="showSummary">
                      <i class="fa fa-list"></i> Show Summary
                    </button>
                  </div>

                  <!-- Summary Section -->
                  <div id="fineSummary" style="display: none; margin-top: 15px;">
                    <div class="alert alert-info">
                      <h5><i class="fa fa-info-circle"></i> Fine Rules Summary:</h5>
                      <div id="summaryContent"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group" id="fineJsonHelp">
            <label class="col-md-2 control-label"></label>
            <div class="col-md-7">
              <div class="alert alert-info" style="margin-bottom:0;">
              <strong>Fine Algorithm Examples:</strong><br>
              <b>Fixed Fine:</b> Enter a fixed amount (e.g., 100) - Applied once when installment becomes overdue<br>
              <b>JSON (Days/Weeks/Months):</b> <code>[{"days":30,"fine":10},{"days":20,"fine":20},{"days":"no_day","fine":30}]</code><br>
              <small>- First 30 days: 10 rupees, Next 20 days: 20 rupees, After 50 days: 30 rupees</small><br>
              <b>Bimonthly:</b> <code>[{"bimonth":2,"fine":400},{"bimonth":"no_bimonth","fine":500}]</code><br>
              <small>- First 2 months: 400 rupees, After 2 months: 500 rupees (cumulative)</small><br>
              <b>Date Range:</b> <code>[{"start_date":"2025-01-01","end_date":"2025-01-10","fine":30},{"start_date":"2025-01-11","end_date":"2025-12-31","fine":60}]</code><br>
              <small>- Jan 1-10: 30 rupees, Jan 11 onwards: 30+60=90 rupees (cumulative)</small><br>
              </div>
            </div>
          </div>

        </div>
        <div class="panel-footer">
          <center>
            <input type="submit" class="btn btn-primary" value="Submit"/>
            <a href="<?php echo site_url('feesv2/fees_blueprint');?>" class="btn btn-warning">Cancel</a>
          </center>
        </div>
      </div>
    </div>
  </div>
  </form>
</div>

<script type="text/javascript">
  $('#discount_amount_algo').on('change',function(){
    var algo = $('#discount_amount_algo').val();
    if (algo == 'discount_if_full_paid_num') {
      $('#discountAmount').show();
    }else if(algo == 'discount_if_full_paid_p'){
      $('#discountAmount').show();
    }else{
      $('#discountAmount').hide();
      $('#discount_amountId').val('');
    }
  });

$('#staff_discount_amount_algo').on('change',function(){
    var algo = $('#staff_discount_amount_algo').val();
    if (algo == 'discount_if_full_paid_num') {
      $('#discountAmount').show();
    }else if(algo == 'discount_if_full_paid_p'){
      $('#staff_discountAmount').show();
    }else{
      $('#staff_discountAmount').hide();
      $('#discount_amountId').val('');
    }
  });

   $('#fine_amount_algo').on('change',function(){
    var algo = $('#fine_amount_algo').val();

    // Hide all sections first
    $('#fineAmount').hide();
    $('#previousfineAmount').hide();
    $('#fineJsonHelp').hide();
    $('#fineJsonBuilder').hide();
    $('#fine_amount_params').val('');

    if (algo == 'fine_per_day' || algo == 'fixed_fine' || algo == 'fine_per_week' || algo == 'fine_per_month') {
      get_previous_fixed_fine_amount();
      $('#fineAmount').show();
      $('#previousfineAmount').show();

      // Set appropriate placeholder for fixed fine
      if (algo == 'fixed_fine') {
        $('#fine_amount_params').attr('placeholder', 'Enter fixed amount (e.g., 100)');
      }
    } else if (algo == 'fine_json' || algo == 'fine_json_bimonthly' || algo == 'fine_date_range') {
      $('#fineAmount').show();
      $('#fineJsonHelp').show();
      $('#fineJsonBuilder').show();

      // Initialize the dynamic builder
      initializeFineBuilder(algo);

      // Set placeholder text based on selected JSON type
      var placeholder = '';
      switch(algo) {
        case 'fine_json':
          placeholder = '[{"days":30,"fine":10},{"days":20,"fine":20},{"days":"no_day","fine":30}]';
          break;
        case 'fine_json_bimonthly':
          placeholder = '[{"bimonth":2,"fine":100},{"bimonth":"no_bimonth","fine":200}]';
          break;
        case 'fine_date_range':
          placeholder = '[{"start_date":"2025-01-01","end_date":"2025-01-10","fine":30},{"start_date":"2025-01-11","end_date":"2025-12-31","fine":60}]';
          break;
      }
      $('#fine_amount_params').attr('placeholder', placeholder);
    }
  });

  function get_previous_fixed_fine_amount(){
    let bpId = '<?php echo $feev2_blueprint_id ?>';
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_blueprint/get_previous_fine_alog_amount'); ?>',
        type: "post",
        data:{'bpId':bpId},
        success: function (data) {
          var res = JSON.parse(data);
          $('#previous_amount_assinged').val(res.fine_amount_params);
        },
        error: function (err) {
          console.log(err);
        }
    });
  }

  // Global variables for fine builder
  var currentFineType = '';
  var fineRuleCounter = 0;

  // Initialize fine builder based on algorithm type
  function initializeFineBuilder(algo) {
    currentFineType = algo;
    fineRuleCounter = 0;
    $('#fineConfigContainer').empty();

    // Update builder title and description
    var titles = {
      'fine_json': {
        title: 'Days/Weeks/Months Fine Builder',
        description: 'Configure fine amounts based on overdue periods (days, weeks, or months)'
      },
      'fine_json_bimonthly': {
        title: 'Bimonthly Fine Builder',
        description: 'Configure fine amounts based on 2-month periods'
      },
      'fine_date_range': {
        title: 'Date Range Fine Builder',
        description: 'Configure fine amounts based on specific date ranges'
      }
    };

    $('#builderTitle').text(titles[algo].title);
    $('#builderDescription').text(titles[algo].description);

    addFineRule();
  }

  // Add new fine rule
  $('#addFineRule').on('click', function() {
    addFineRule();
  });

  // Preview JSON
  $('#previewJson').on('click', function() {
    var jsonData = buildJsonFromUI();
    $('#fine_amount_params').val(JSON.stringify(jsonData, null, 2));
    alert('JSON has been generated and populated in the text field below.');
  });

  // Load from JSON
  $('#loadFromJson').on('click', function() {
    var jsonText = $('#fine_amount_params').val();
    if (!jsonText.trim()) {
      alert('Please enter JSON data in the text field below first.');
      return;
    }

    try {
      var jsonData = JSON.parse(jsonText);
      if (!Array.isArray(jsonData)) {
        alert('JSON must be an array format');
        return;
      }

      loadJsonToUI(jsonData);
      alert('JSON data has been loaded into the form builder.');
    } catch (error) {
      alert('Invalid JSON format: ' + error.message);
    }
  });

  // Show summary
  $('#showSummary').on('click', function() {
    var jsonData = buildJsonFromUI();
    var summaryHtml = generateSummary(jsonData);
    $('#summaryContent').html(summaryHtml);
    $('#fineSummary').toggle();
  });

  function generateSummary(jsonData) {
    if (jsonData.length === 0) {
      return '<p>No fine rules configured.</p>';
    }

    var html = '<ul>';
    jsonData.forEach(function(rule, index) {
      html += '<li><strong>Rule ' + (index + 1) + ':</strong> ';

      switch(currentFineType) {
        case 'fine_json':
          var periodType = Object.keys(rule).find(key => key !== 'fine');
          var periodValue = rule[periodType];
          html += 'After ' + periodValue + ' ' + periodType + ' → ₹' + rule.fine;
          break;

        case 'fine_json_bimonthly':
          html += 'After ' + rule.bimonth + ' bimonth(s) → ₹' + rule.fine;
          break;

        case 'fine_date_range':
          html += 'From ' + rule.start_date + ' to ' + rule.end_date + ' → ₹' + rule.fine;
          break;
      }

      html += '</li>';
    });
    html += '</ul>';

    return html;
  }

  function addFineRule() {
    fineRuleCounter++;
    var ruleHtml = '';

    switch(currentFineType) {
      case 'fine_json':
        ruleHtml = buildDaysWeeksMonthsRule(fineRuleCounter);
        break;
      case 'fine_json_bimonthly':
        ruleHtml = buildBimonthlyRule(fineRuleCounter);
        break;
      case 'fine_date_range':
        ruleHtml = buildDateRangeRule(fineRuleCounter);
        break;
    }

    $('#fineConfigContainer').append(ruleHtml);
  }

  function buildDaysWeeksMonthsRule(counter) {
    return `
      <div class="fine-rule" id="rule_${counter}" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 5px;">
        <div class="row">
          <div class="col-md-1">
            <div class="rule-counter">${counter}</div>
          </div>
          <div class="col-md-3">
            <label><i class="fa fa-calendar"></i> Period Type:</label>
            <select class="form-control period-type" name="period_type_${counter}">
              <option value="days">Days</option>
              <option value="weeks">Weeks</option>
              <option value="months">Months</option>
            </select>
          </div>
          <div class="col-md-3">
            <label><i class="fa fa-clock-o"></i> Period Value:</label>
            <input type="text" class="form-control period-value" name="period_value_${counter}" placeholder="30 or no_day">
            <div class="period-help">Enter number or "no_day/no_week/no_month" for unlimited</div>
          </div>
          <div class="col-md-3">
            <label><i class="fa fa-money"></i> Fine Amount (₹):</label>
            <input type="number" class="form-control fine-amount" name="fine_amount_${counter}" placeholder="100" step="0.01" min="0">
          </div>
          <div class="col-md-2">
            <label>&nbsp;</label><br>
            <button type="button" class="btn btn-danger btn-sm remove-rule" data-rule="rule_${counter}" title="Remove this rule">
              <i class="fa fa-trash"></i> Remove
            </button>
          </div>
        </div>
      </div>
    `;
  }

  function buildBimonthlyRule(counter) {
    return `
      <div class="fine-rule" id="rule_${counter}" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 5px;">
        <div class="row">
          <div class="col-md-2">
            <div class="rule-counter">${counter}</div>
          </div>
          <div class="col-md-4">
            <label><i class="fa fa-calendar-o"></i> Bimonth Period:</label>
            <input type="text" class="form-control bimonth-value" name="bimonth_value_${counter}" placeholder="2 or no_bimonth">
            <div class="period-help">Enter number of 2-month periods or "no_bimonth" for unlimited</div>
          </div>
          <div class="col-md-4">
            <label><i class="fa fa-money"></i> Fine Amount (₹):</label>
            <input type="number" class="form-control fine-amount" name="fine_amount_${counter}" placeholder="400" step="0.01" min="0">
          </div>
          <div class="col-md-2">
            <label>&nbsp;</label><br>
            <button type="button" class="btn btn-danger btn-sm remove-rule" data-rule="rule_${counter}" title="Remove this rule">
              <i class="fa fa-trash"></i> Remove
            </button>
          </div>
        </div>
      </div>
    `;
  }

  function buildDateRangeRule(counter) {
    return `
      <div class="fine-rule" id="rule_${counter}" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 5px;">
        <div class="row">
          <div class="col-md-1">
            <div class="rule-counter">${counter}</div>
          </div>
          <div class="col-md-3">
            <label><i class="fa fa-calendar"></i> Start Date:</label>
            <input type="date" class="form-control start-date" name="start_date_${counter}">
            <div class="period-help">When this fine rule becomes active</div>
          </div>
          <div class="col-md-3">
            <label><i class="fa fa-calendar"></i> End Date:</label>
            <input type="date" class="form-control end-date" name="end_date_${counter}">
            <div class="period-help">When this fine rule expires (optional)</div>
          </div>
          <div class="col-md-3">
            <label><i class="fa fa-money"></i> Fine Amount (₹):</label>
            <input type="number" class="form-control fine-amount" name="fine_amount_${counter}" placeholder="30" step="0.01" min="0">
            <div class="period-help">Fine amount for this date range</div>
          </div>
          <div class="col-md-2">
            <label>&nbsp;</label><br>
            <button type="button" class="btn btn-danger btn-sm remove-rule" data-rule="rule_${counter}" title="Remove this rule">
              <i class="fa fa-trash"></i> Remove
            </button>
          </div>
        </div>
      </div>
    `;
  }

  // Remove rule functionality
  $(document).on('click', '.remove-rule', function() {
    var ruleId = $(this).data('rule');
    $('#' + ruleId).remove();
    updateRuleNumbers();
  });

  // Auto-update JSON when form fields change
  $(document).on('change', '.fine-rule input, .fine-rule select', function() {
    if ($('#fineSummary').is(':visible')) {
      $('#showSummary').click(); // Refresh summary
    }
  });

  function updateRuleNumbers() {
    $('.fine-rule').each(function(index) {
      $(this).find('label:first').text('Rule ' + (index + 1));
    });
  }

  // Add validation for date ranges
  $(document).on('change', '.start-date, .end-date', function() {
    var rule = $(this).closest('.fine-rule');
    var startDate = rule.find('.start-date').val();
    var endDate = rule.find('.end-date').val();

    if (startDate && endDate && startDate > endDate) {
      alert('Start date cannot be after end date');
      $(this).val('');
    }
  });

  // Add validation for numeric fields
  $(document).on('input', '.fine-amount', function() {
    var value = parseFloat($(this).val());
    if (value < 0) {
      $(this).val(0);
    }
  });

  function loadJsonToUI(jsonData) {
    $('#fineConfigContainer').empty();
    fineRuleCounter = 0;

    jsonData.forEach(function(item) {
      fineRuleCounter++;
      var ruleHtml = '';

      switch(currentFineType) {
        case 'fine_json':
          ruleHtml = buildDaysWeeksMonthsRule(fineRuleCounter);
          $('#fineConfigContainer').append(ruleHtml);

          // Populate values
          var currentRule = $('#rule_' + fineRuleCounter);
          if (item.days !== undefined) {
            currentRule.find('.period-type').val('days');
            currentRule.find('.period-value').val(item.days);
          } else if (item.weeks !== undefined) {
            currentRule.find('.period-type').val('weeks');
            currentRule.find('.period-value').val(item.weeks);
          } else if (item.months !== undefined) {
            currentRule.find('.period-type').val('months');
            currentRule.find('.period-value').val(item.months);
          }
          currentRule.find('.fine-amount').val(item.fine);
          break;

        case 'fine_json_bimonthly':
          ruleHtml = buildBimonthlyRule(fineRuleCounter);
          $('#fineConfigContainer').append(ruleHtml);

          var currentRule = $('#rule_' + fineRuleCounter);
          currentRule.find('.bimonth-value').val(item.bimonth);
          currentRule.find('.fine-amount').val(item.fine);
          break;

        case 'fine_date_range':
          ruleHtml = buildDateRangeRule(fineRuleCounter);
          $('#fineConfigContainer').append(ruleHtml);

          var currentRule = $('#rule_' + fineRuleCounter);
          currentRule.find('.start-date').val(item.start_date);
          currentRule.find('.end-date').val(item.end_date);
          currentRule.find('.fine-amount').val(item.fine);
          break;
      }
    });
  }

  function buildJsonFromUI() {
    var jsonArray = [];

    $('.fine-rule').each(function() {
      var rule = {};

      switch(currentFineType) {
        case 'fine_json':
          var periodType = $(this).find('.period-type').val();
          var periodValue = $(this).find('.period-value').val();
          var fineAmount = parseFloat($(this).find('.fine-amount').val()) || 0;

          // Handle no_period cases
          if (periodValue.toLowerCase().includes('no_')) {
            rule[periodType.slice(0, -1)] = 'no_' + periodType.slice(0, -1);
          } else {
            rule[periodType.slice(0, -1)] = parseInt(periodValue) || 0;
          }
          rule.fine = fineAmount;
          break;

        case 'fine_json_bimonthly':
          var bimonthValue = $(this).find('.bimonth-value').val();
          var fineAmount = parseFloat($(this).find('.fine-amount').val()) || 0;

          if (bimonthValue.toLowerCase() === 'no_bimonth') {
            rule.bimonth = 'no_bimonth';
          } else {
            rule.bimonth = parseInt(bimonthValue) || 0;
          }
          rule.fine = fineAmount;
          break;

        case 'fine_date_range':
          var startDate = $(this).find('.start-date').val();
          var endDate = $(this).find('.end-date').val();
          var fineAmount = parseFloat($(this).find('.fine-amount').val()) || 0;

          rule.start_date = startDate;
          rule.end_date = endDate;
          rule.fine = fineAmount;
          break;
      }

      if (Object.keys(rule).length > 0) {
        jsonArray.push(rule);
      }
    });

    return jsonArray;
  }

  // Add form validation before submit
  $('#demo-form').on('submit', function(e) {
    var algo = $('#fine_amount_algo').val();
    var params = $('#fine_amount_params').val();

    // If using UI builder, generate JSON from UI
    if ((algo == 'fine_json' || algo == 'fine_json_bimonthly' || algo == 'fine_date_range') && $('#fineJsonBuilder').is(':visible')) {
      var jsonData = buildJsonFromUI();
      $('#fine_amount_params').val(JSON.stringify(jsonData));
      params = $('#fine_amount_params').val();
    }

    if ((algo == 'fine_json' || algo == 'fine_json_bimonthly' || algo == 'fine_date_range') && params) {
      try {
        var jsonData = JSON.parse(params);
        if (!Array.isArray(jsonData)) {
          alert('JSON must be an array format');
          e.preventDefault();
          return false;
        }

        // Validate specific formats
        if (algo == 'fine_date_range') {
          for (var i = 0; i < jsonData.length; i++) {
            if (!jsonData[i].start_date || !jsonData[i].end_date || !jsonData[i].fine) {
              alert('Date range format requires start_date, end_date, and fine fields');
              e.preventDefault();
              return false;
            }
          }
        }
      } catch (error) {
        alert('Invalid JSON format. Please check your input.');
        e.preventDefault();
        return false;
      }
    }
  });

  // Initialize tooltips and other UI enhancements
  $(document).ready(function() {
    // Initialize tooltips
    $('[title]').tooltip();

    // Add smooth animations for new rules
    $(document).on('DOMNodeInserted', '.fine-rule', function() {
      $(this).hide().fadeIn(300);
    });
  });

</script>